"use client";

import React, { useEffect } from "react";
import PageLayout from "@/components/layout/PageLayout";
import AOS from "aos";
import "aos/dist/aos.css";

const Company = () => {

  // Initialize AOS animation library
  useEffect(() => {
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: false,
      mirror: true,
    });
  }, []);

  // Add text shadow style
  const textShadowStyle = { textShadow: '1px 1px 1px rgba(0,0,0,0.1)' };
    return (
    <PageLayout
      title="ABOUT US"
      subtitle="Power & Energy Management since 1984"
      category="about"
      textColor="text-blue-800"
    >
      {/* Blue gradient background overlay */}
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-blue-100 to-blue-200 relative overflow-hidden">
        {/* Decorative background elements */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-10 w-32 h-32 bg-blue-400 rounded-full blur-xl animate-pulse"></div>
          <div className="absolute top-40 right-20 w-24 h-24 bg-blue-500 rounded-full blur-lg animate-bounce"></div>
          <div className="absolute bottom-20 left-1/4 w-40 h-40 bg-blue-300 rounded-full blur-2xl animate-pulse"></div>
          <div className="absolute bottom-40 right-1/3 w-28 h-28 bg-blue-600 rounded-full blur-xl animate-bounce"></div>
        </div>

        <div className="relative z-10 max-w-full mx-auto px-2 sm:px-3 md:px-4 py-2 sm:py-3 md:py-4" style={{ fontFamily: 'Open Sans, sans-serif' }}>
          {/* Main Content */}
          <div className="grid grid-cols-1 gap-2 sm:gap-3 lg:grid-cols-2 lg:gap-4 md:gap-5">
            {/* Left Column - Company Information */}
            <div className="space-y-2 sm:space-y-3 md:space-y-4" data-aos="fade-right" data-aos-delay="100">
              <div className="bg-white/80 backdrop-blur-sm rounded-xl p-3 sm:p-4 md:p-6 shadow-lg border border-blue-200">
                <h2 className="text-lg sm:text-xl md:text-2xl font-bold mb-2 sm:mb-3 md:mb-4 text-blue-800 bg-gradient-to-r from-blue-200 to-blue-300 py-2 px-3 rounded-lg shadow-sm text-center" style={textShadowStyle}>Our Story</h2>
                <div className="space-y-2 sm:space-y-3">
                  <p className="text-blue-900 leading-relaxed font-medium text-sm sm:text-base md:text-lg text-justify" data-aos="fade-up" data-aos-delay="100">
                    Atandra Energy Pvt. Ltd., headquartered in Chennai, draws upon a rich foundation of more than 39+ years of expertise in the realm of Power & Energy Management.
                  </p>
                  <p className="text-blue-900 leading-relaxed font-medium text-sm sm:text-base md:text-lg text-justify" data-aos="fade-up" data-aos-delay="200">
                    We offer solutions to industrial & commercial establishments under our popular brand <span className="font-bold text-blue-700 bg-blue-100 px-2 py-1 rounded">KRYKARD</span>. With over 5,00,000 installations of Power Conditioners & over 1,50,000 installations of Portable & Panel Load Managers, KRYKARD is one of the leading brands in Power Conditioning & Energy Management.
                  </p>
                  <p className="text-blue-900 leading-relaxed font-medium text-sm sm:text-base md:text-lg text-justify" data-aos="fade-up" data-aos-delay="300">
                    Our Servo Stabilizers & Transformers have obtained <span className="font-bold text-blue-700 bg-blue-100 px-2 py-1 rounded">CE certification</span>, providing our customers with the assurance that these products adhere to rigorous global health, safety, & environmental protection standards.
                  </p>
                </div>
              </div>

              <div className="bg-white/80 backdrop-blur-sm rounded-xl p-3 sm:p-4 md:p-6 shadow-lg border border-blue-200" data-aos="fade-up" data-aos-delay="300">
                <h3 className="text-base sm:text-lg md:text-xl font-semibold mb-2 sm:mb-3 text-blue-800 bg-gradient-to-r from-blue-200 to-blue-300 py-2 px-3 rounded-lg shadow-sm text-center" style={textShadowStyle}>Our Facilities</h3>
                <p className="text-blue-900 mb-2 font-medium text-sm sm:text-base md:text-lg text-justify">We have the following facilities:</p>
                <ul className="list-disc pl-4 sm:pl-6 text-blue-900 space-y-1 sm:space-y-2">
                  <li className="leading-relaxed text-sm sm:text-base md:text-lg text-justify" data-aos="fade-right" data-aos-delay="400">
                    <span className="font-bold text-blue-800 underline decoration-blue-500 decoration-2 underline-offset-2">R&D department</span> for Power Electronics & Electro-magnetics.
                  </li>
                  <li className="leading-relaxed text-sm sm:text-base md:text-lg text-justify" data-aos="fade-right" data-aos-delay="600">
                    <span className="font-bold text-blue-800 underline decoration-blue-500 decoration-2 underline-offset-2">Software Development department</span> for energy management software & Industry 4.0 solutions.
                  </li>
                </ul>
              </div>
            </div>

            {/* Right Column - Achievements & Stats */}
            <div className="space-y-2 sm:space-y-3 md:space-y-4" data-aos="fade-left" data-aos-delay="200">
              {/* Achievements Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3 md:gap-4">
                {/* Achievement 1 */}
                <div
                  className="bg-gradient-to-r from-blue-50 via-white to-blue-50 rounded-lg p-3 sm:p-4 md:p-6 text-center flex flex-col items-center shadow-md relative overflow-hidden border border-blue-200"
                  data-aos="zoom-in"
                  data-aos-delay="100"
                >
                  <div className="mb-2 sm:mb-3 relative">
                    <div className="flex items-center justify-center">
                      {/* Left laurel wreath */}
                      <img
                        src="/images/icons/laurel-wreath.svg"
                        alt="Left laurel wreath"
                        className="h-6 w-6 sm:h-8 sm:w-8 md:h-12 md:w-12 absolute -left-1 top-1/2 transform -translate-y-1/2"
                        style={{ transform: "translate(-30%, -50%) scaleX(-1)" }}
                      />

                      {/* Number 1 */}
                      <div className="h-12 w-12 sm:h-16 sm:w-16 md:h-20 md:w-20 mx-auto flex items-center justify-center bg-gradient-to-b from-blue-400 to-blue-600 rounded-full z-10 shadow-md">
                        <span className="text-sm sm:text-lg md:text-xl font-bold text-white drop-shadow-md">NO. 1</span>
                      </div>

                      {/* Right laurel wreath */}
                      <img
                        src="/images/icons/laurel-wreath.svg"
                        alt="Right laurel wreath"
                        className="h-6 w-6 sm:h-8 sm:w-8 md:h-12 md:w-12 absolute -right-1 top-1/2 transform -translate-y-1/2"
                        style={{ transform: "translate(30%, -50%)" }}
                      />
                    </div>
                  </div>
                  <h3 className="text-xs sm:text-sm md:text-base font-bold text-blue-900 mb-1">INDIA'S NO. 1</h3>
                  <p className="font-semibold text-blue-800 text-xs sm:text-sm md:text-base text-center">MANUFACTURER OF SERVO STABILISERS</p>
                </div>

                {/* Achievement 2 - 100+ SERVICE CENTERS */}
                <div
                  className="bg-white/90 backdrop-blur-sm p-3 sm:p-4 md:p-6 rounded-lg shadow-md border-l-4 border-l-blue-500 hover:shadow-xl transition-all duration-300 group"
                  data-aos="fade-up-right"
                  data-aos-delay="200"
                >
                  <div className="flex flex-col items-center">
                    <div className="p-2 sm:p-3 bg-blue-50 rounded-lg mb-2 group-hover:bg-blue-100 transition-colors duration-300 flex items-center justify-center">
                      <svg className="h-6 w-6 sm:h-8 sm:w-8 md:h-12 md:w-12 text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.25">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    </div>
                    <div className="text-center">
                      <h3 className="text-xs sm:text-sm md:text-base font-medium text-blue-700 group-hover:text-blue-800 transition-colors duration-300 tracking-wide">100+ SERVICE CENTERS</h3>
                      <div className="h-0.5 w-8 sm:w-12 md:w-16 bg-blue-500 mt-1 mx-auto group-hover:w-12 sm:group-hover:w-20 md:group-hover:w-24 transition-all duration-500"></div>
                    </div>
                  </div>
                </div>

                {/* Achievement 3 */}
                <div
                  className="bg-white/90 backdrop-blur-sm p-3 sm:p-4 md:p-6 rounded-lg shadow-md border-l-4 border-l-blue-600 hover:shadow-lg transition-all duration-300"
                  data-aos="fade-up-left"
                  data-aos-delay="300"
                >
                  <div className="flex items-center mb-2">
                    <div className="p-2 bg-blue-100 rounded-full mr-3">
                      <svg className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6 text-blue-700" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                    </div>
                    <h3 className="text-xs sm:text-sm md:text-base font-bold text-blue-700">PREFERRED SUPPLIER OF</h3>
                  </div>
                  <p className="font-semibold text-blue-800 ml-8 sm:ml-10 md:ml-12 text-xs sm:text-sm md:text-base">LARGE CORPORATES & OEM'S</p>
                </div>

                {/* Achievement 4 */}
                <div
                  className="bg-white/90 backdrop-blur-sm p-3 sm:p-4 md:p-6 rounded-lg shadow-md border-l-4 border-l-blue-700 hover:shadow-lg transition-all duration-300"
                  data-aos="fade-up-right"
                  data-aos-delay="400"
                >
                  <div className="flex items-center mb-2">
                    <div className="p-2 bg-blue-100 rounded-full mr-3">
                      <svg className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6 text-blue-700" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="12" cy="12" r="10" strokeWidth="2" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 11l6 0" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11l-3 5.196" />
                      </svg>
                    </div>
                    <h3 className="text-xs sm:text-sm md:text-base font-bold text-blue-700">CE CERTIFIED PRODUCTS</h3>
                  </div>
                </div>

                {/* Achievement 5 - Experience */}
                <div
                  className="bg-gradient-to-r from-blue-100 via-blue-200 to-blue-300 rounded-lg p-3 sm:p-4 md:p-6 text-center flex flex-col items-center col-span-1 sm:col-span-2 shadow-md"
                  data-aos="zoom-in-up"
                  data-aos-delay="500"
                >
                  <div className="mb-2 relative">
                    <div className="rounded-full bg-blue-700 h-12 w-12 sm:h-16 sm:w-16 md:h-20 md:w-20 flex items-center justify-center shadow-md">
                      <span className="text-sm sm:text-lg md:text-2xl font-bold text-white">39+</span>
                    </div>
                  </div>
                  <h3 className="text-sm sm:text-base md:text-lg font-bold text-blue-800">39+ YEARS EXPERIENCE</h3>
                </div>
              </div>
            </div>
          </div>

          {/* Certifications and Commitment Sections - Side by Side Structure */}
          <div className="my-4 sm:my-6 md:my-8">
            <div className="grid grid-cols-1 gap-3 sm:gap-4 lg:grid-cols-2 lg:gap-4 md:gap-5">
              {/* Left Section - Certifications */}
              <div
                className="transform transition-all duration-700 hover:scale-105"
                data-aos="fade-right"
                data-aos-delay="100"
                data-aos-offset="200"
              >
                <div className="relative overflow-hidden bg-gradient-to-r from-blue-200 via-blue-100 to-blue-200 rounded-xl shadow-lg border border-blue-300 h-full">
                  {/* Animated background elements */}
                  <div className="absolute top-0 left-0 w-full h-full overflow-hidden opacity-20">
                    <div className="absolute top-5 left-5 w-16 h-16 rounded-full bg-blue-400 animate-pulse" data-aos="zoom-in" data-aos-delay="200"></div>
                    <div className="absolute bottom-8 right-8 w-24 h-24 rounded-full bg-blue-300 animate-ping opacity-30" data-aos="zoom-in" data-aos-delay="400"></div>
                    <div className="absolute top-1/2 left-1/2 w-32 h-32 rounded-full bg-blue-400 -translate-x-1/2 -translate-y-1/2 opacity-20" data-aos="zoom-in" data-aos-delay="600"></div>
                  </div>

                  {/* Content */}
                  <div className="relative z-10 p-3 sm:p-4 md:p-6">
                    {/* Header with shine effect */}
                    <div className="relative overflow-hidden mb-3 sm:mb-4">
                      <h2 className="text-lg sm:text-xl md:text-2xl font-bold text-blue-800 text-center" style={textShadowStyle}>
                        Certifications
                      </h2>
                      <div className="absolute top-0 -inset-x-20 h-full w-1/3 bg-gradient-to-r from-transparent via-blue-100 to-transparent skew-x-12 animate-shine"></div>
                    </div>
                    <p className="text-blue-800 mb-3 sm:mb-4 font-medium text-sm sm:text-base md:text-lg text-center text-justify">
                      Our organization has the following certifications:
                    </p>
                    {/* Certification badges with hover effects */}
                    <div className="grid grid-cols-2 gap-2 sm:gap-3">
                      <div
                        className="bg-blue-100 rounded-lg shadow-md p-2 sm:p-3 hover:shadow-lg transition-all duration-300 hover:bg-blue-50 hover:-translate-y-1 border-l-4 border-blue-600"
                        data-aos="flip-left"
                        data-aos-delay="100"
                      >
                        <p className="text-blue-900 font-bold text-center text-xs sm:text-sm md:text-base">ISO 9001:2015</p>
                      </div>
                      <div
                        className="bg-blue-100 rounded-lg shadow-md p-2 sm:p-3 hover:shadow-lg transition-all duration-300 hover:bg-blue-50 hover:-translate-y-1 border-l-4 border-blue-600"
                        data-aos="flip-right"
                        data-aos-delay="200"
                      >
                        <p className="text-blue-900 font-bold text-center text-xs sm:text-sm md:text-base">14001 – 2015</p>
                      </div>
                      <div
                        className="bg-blue-100 rounded-lg shadow-md p-2 sm:p-3 hover:shadow-lg transition-all duration-300 hover:bg-blue-50 hover:-translate-y-1 border-l-4 border-blue-600"
                        data-aos="flip-left"
                        data-aos-delay="300"
                      >
                        <p className="text-blue-900 font-bold text-center text-xs sm:text-sm md:text-base">45001 – 2018</p>
                      </div>
                      <div
                        className="bg-blue-100 rounded-lg shadow-md p-2 sm:p-3 hover:shadow-lg transition-all duration-300 hover:bg-blue-50 hover:-translate-y-1 border-l-4 border-blue-600"
                        data-aos="flip-right"
                        data-aos-delay="400"
                      >
                        <p className="text-blue-900 font-bold text-center text-xs sm:text-sm md:text-base">50001</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Section - Our Commitment */}
              <div
                className="transform transition-all duration-700 hover:scale-105"
                data-aos="fade-left"
                data-aos-delay="300"
                data-aos-offset="200"
              >
                <div className="relative overflow-hidden bg-gradient-to-r from-blue-300 via-blue-200 to-blue-300 rounded-xl shadow-lg border border-blue-400 h-full">
                  {/* Animated background elements */}
                  <div className="absolute top-0 left-0 w-full h-full overflow-hidden opacity-20">
                    <div className="absolute top-8 right-8 w-20 h-20 rounded-full bg-blue-500 animate-pulse" data-aos="zoom-in" data-aos-delay="300"></div>
                    <div className="absolute bottom-4 left-4 w-24 h-24 rounded-full bg-blue-400 animate-ping opacity-30" data-aos="zoom-in" data-aos-delay="500"></div>
                    <div className="absolute top-1/2 left-1/2 w-32 h-32 rounded-full bg-blue-500 -translate-x-1/2 -translate-y-1/2 opacity-20" data-aos="zoom-in" data-aos-delay="700"></div>
                  </div>

                  {/* Content */}
                  <div className="relative z-10 p-3 sm:p-4 md:p-6">
                    {/* Header with shine effect */}
                    <div className="relative overflow-hidden mb-3 sm:mb-4">
                      <h2 className="text-lg sm:text-xl md:text-2xl font-bold text-blue-800 text-center" style={textShadowStyle}>
                        Our Commitment
                      </h2>
                      <div className="absolute top-0 -inset-x-20 h-full w-1/3 bg-gradient-to-r from-transparent via-blue-100 to-transparent skew-x-12 animate-shine"></div>
                    </div>
                    <div className="space-y-2 sm:space-y-3">
                      <p className="text-blue-900 leading-relaxed font-medium text-sm sm:text-base md:text-lg text-justify" data-aos="fade-up" data-aos-delay="100">
                        State-of-the-art facilities empower us to address the requirements of Indian industries <span className="relative inline-block">
                          <span className="relative z-10 font-bold text-blue-900 bg-blue-200 px-2 py-1 rounded">
                            comprehensively, effectively & efficiently
                          </span>
                          <span className="absolute inset-0 bg-blue-300 rounded animate-pulse opacity-30"></span>
                        </span>, ensuring they derive maximum benefits from the power conditioning & energy management solutions we provide.
                      </p>
                      <p className="text-blue-900 leading-relaxed font-medium text-sm sm:text-base md:text-lg text-justify" data-aos="fade-up" data-aos-delay="300">
                        With a taskforce of around <span className="relative inline-block">
                          <span className="relative z-10 font-bold text-blue-700 bg-blue-100 px-2 py-1 rounded">
                            500+ employees
                          </span>
                          <span className="absolute inset-0 bg-blue-200 rounded animate-pulse opacity-30"></span>
                        </span> & an extensive network of sales & service branches nationwide, we are well-equipped to seamlessly reach out to our customers & fulfil their needs.
                      </p>
                    </div>
                    {/* Decorative elements */}
                    <div className="mt-4 grid grid-cols-4 gap-2">
                      <div className="h-1 bg-blue-400 rounded animate-pulse" data-aos="fade-right" data-aos-delay="100"></div>
                      <div className="h-1 bg-blue-500 rounded animate-pulse delay-100" data-aos="fade-right" data-aos-delay="200"></div>
                      <div className="h-1 bg-blue-600 rounded animate-pulse delay-200" data-aos="fade-right" data-aos-delay="300"></div>
                      <div className="h-1 bg-blue-700 rounded animate-pulse delay-300" data-aos="fade-right" data-aos-delay="400"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Need More Information Section - Blue theme */}
          <div className="my-4 sm:my-6 md:my-8">
            <div
              className="relative overflow-hidden bg-gradient-to-r from-blue-100 via-blue-200 to-blue-300 rounded-xl shadow-lg p-3 sm:p-4 md:p-6 border border-blue-300"
              data-aos="fade-up"
              data-aos-offset="200"
              data-aos-duration="1000"
            >
              {/* Animated background elements */}
              <div className="absolute top-0 left-0 w-full h-full overflow-hidden opacity-10">
                <div className="absolute top-8 right-8 w-20 h-20 rounded-full bg-blue-500 animate-pulse" data-aos="fade-down-left" data-aos-delay="200"></div>
                <div className="absolute bottom-8 left-8 w-24 h-24 rounded-full bg-blue-400 animate-ping opacity-30" data-aos="fade-up-right" data-aos-delay="400"></div>
                <div className="absolute top-1/2 left-1/2 w-32 h-32 rounded-full bg-blue-600 -translate-x-1/2 -translate-y-1/2 opacity-20" data-aos="zoom-in" data-aos-delay="600"></div>
              </div>

              {/* Content */}
              <div className="relative z-10 text-center max-w-4xl mx-auto">
                {/* Header with shine effect */}
                <div className="relative overflow-hidden mb-3 sm:mb-4">
                  <h2 className="text-lg sm:text-xl md:text-2xl font-bold text-blue-800 mb-2" style={textShadowStyle}>
                    Need More Information?
                  </h2>
                  <div className="absolute top-0 -inset-x-20 h-full w-1/3 bg-gradient-to-r from-transparent via-blue-100 to-transparent skew-x-12 animate-shine"></div>
                </div>
                <p className="text-blue-900 mb-4 sm:mb-6 leading-relaxed font-medium text-sm sm:text-base md:text-lg text-justify" data-aos="fade-up" data-aos-delay="200">
                  Our team of experts is ready to help you with product specifications, custom solutions, pricing, and any other details you need about the <span className="font-bold text-blue-700 bg-blue-100 px-2 py-1 rounded">KRYKARD</span> Static Voltage Regulator.
                </p>

                {/* Contact button with animation - blue gradient */}
                <div className="relative inline-block group" data-aos="zoom-in" data-aos-delay="400">
                  {/* Button glow effect */}
                  <div className="absolute -inset-1 bg-gradient-to-r from-blue-400 to-blue-600 rounded-lg blur opacity-30 group-hover:opacity-80 transition duration-500"></div>
                  <a href="/contact/sales" className="relative inline-flex items-center justify-center px-4 sm:px-6 md:px-8 py-2 sm:py-3 text-sm sm:text-base md:text-lg font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg shadow-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform group-hover:scale-105">
                    Contact Our Experts
                    <svg className="w-4 h-4 sm:w-5 sm:h-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Add the animations */}
        <style dangerouslySetInnerHTML={{ __html: `
          * {
            font-family: 'Open Sans', sans-serif !important;
          }

          @keyframes shine {
            0% {
              left: -100%;
            }
            100% {
              left: 100%;
            }
          }

          @keyframes pulse {
            0%, 100% {
              opacity: 0.2;
            }
            50% {
              opacity: 0.5;
            }
          }

          @keyframes ping {
            0% {
              transform: scale(1);
              opacity: 0.8;
            }
            75%, 100% {
              transform: scale(1.5);
              opacity: 0;
            }
          }

          @keyframes float {
            0%, 100% {
              transform: translateY(0);
            }
            50% {
              transform: translateY(-10px);
            }
          }

          @keyframes bounce {
            0%, 100% {
              transform: translateY(0);
            }
            50% {
              transform: translateY(-10px);
            }
          }

          .animate-shine {
            animation: shine 3s infinite;
          }

          .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
          }

          .animate-ping {
            animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
          }

          .animate-float {
            animation: float 3s ease-in-out infinite;
          }

          .animate-bounce {
            animation: bounce 2s ease-in-out infinite;
          }

          .delay-100 {
            animation-delay: 100ms;
          }

          .delay-200 {
            animation-delay: 200ms;
          }

          .delay-300 {
            animation-delay: 300ms;
          }

          /* Responsive text alignment */
          @media (max-width: 640px) {
            .text-justify {
              text-align: left;
            }
          }
        ` }} />
      </div>
    </PageLayout>
  );
};

export default Company;